#!/usr/bin/env python3
"""
Debug test to see the exact Node.js output
"""

import sys
import os
import json
import subprocess

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_node_script_debug():
    """Test the Node.js script directly with debug output"""
    
    print("🧪 DEBUGGING NODE.JS SCRIPT OUTPUT")
    print("=" * 60)
    
    try:
        from services.google_translate_api_service_new import google_translate_service
        
        script_path = google_translate_service.node_script_path
        print(f"📁 Script path: {script_path}")
        
        # Test data
        test_data = {
            'ai_response': 'நிதி சந்தையில் முதலீடு செய்வது பற்றிய விரிவான வழிகாட்டுதல். அடிப்படை அறிவைப் பெறுதல் முக்கியம்.',
            'related_questions': [
                'நிதி சந்தையில் முதலீடு செய்வது எப்படி?',
                'பங்குச் சந்தையில் முதலீடு செய்வது பாதுகாப்பானதா?'
            ],
            'sentence_analysis': [
                {
                    'sentence': 'நிதி சந்தையில் முதலீடு செய்வது பற்றிய விரிவான வழிகாட்டுதல்.',
                    'sentiment': 'neutral',
                    'confidence': 0.8
                },
                {
                    'sentence': 'அடிப்படை அறிவைப் பெறுதல் முக்கியம்.',
                    'sentiment': 'positive', 
                    'confidence': 0.9
                }
            ]
        }
        
        data_json = json.dumps(test_data)
        
        print(f"\n📝 Input data: {data_json[:200]}...")
        print(f"🎯 Target language: Tamil")
        
        # Run Node.js script directly
        result = subprocess.run([
            'node', script_path, 'translate-response',
            data_json, 'Tamil'
        ], capture_output=True, text=True, encoding='utf-8', timeout=60)
        
        print(f"\n📊 RESULTS:")
        print(f"Return code: {result.returncode}")
        print(f"STDOUT: {result.stdout}")
        print(f"STDERR: {result.stderr}")
        
        if result.returncode == 0:
            try:
                response = json.loads(result.stdout)
                print(f"\n✅ SUCCESS!")
                print(f"Response success: {response.get('success', False)}")
                
                if response.get('success'):
                    data = response.get('data', {})
                    ai_response = data.get('ai_response', '')
                    print(f"AI Response: {ai_response}")
                    
                    # Check for reference numbers
                    if '[1]' in ai_response or '[2]' in ai_response:
                        print("✅ Reference numbers found!")
                    else:
                        print("⚠️ No reference numbers found")
                        
                    if data.get('ai_response_error'):
                        print(f"⚠️ AI Response Error: {data.get('ai_response_error')}")
                        
                else:
                    print(f"❌ Node.js script failed: {response.get('error', 'Unknown error')}")
                    
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse JSON response: {e}")
        else:
            print(f"❌ Node.js script failed with return code {result.returncode}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_node_script_debug()
