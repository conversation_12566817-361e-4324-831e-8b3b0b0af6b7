import React from 'react';
import WebpagePreview from '../components/chatComponents/WebpagePreview';
import WebpagePreviewEnhanced from '../components/chatComponents/WebpagePreviewEnhanced';

const ReferenceUIDemo = () => {
  // Sample FAISS data from the backend logs
  const sampleFaissData = [
    {
      url: 'AIdoctestdata 1.xlsx',
      summary: 'நிதி சந்தை (Financial Market) என்பது பணம் மற்றும் நிதி சொத்துகளை வாங்கவும் விற்கவும் அனுமதிக்கும் ஒரு தளமாகும்.',
      source_title: 'AIdoctestdata 1.xlsx',
      source_type: 'news-11-chunk-0',
      file_id: '1745737736292',
      page: '8',
      page_content: 'id: 9652 | file_index: index_demo | file_id: 1745737736292 | Financial markets are essential components of the economy...',
      vector_id: 'news-11-chunk-0',
      file_uploaded: 'AIdoctestdata 1.xlsx'
    },
    {
      url: 'AIdoctestdata 1.xlsx',
      summary: 'இது பொருளாதாரத்தின் முக்கியமான பகுதியாகும், ஏனெனில் இது மூலதனத்தை திறம்பட ஒதுக்க உதவுகிறது.',
      source_title: 'AIdoctestdata 1.xlsx',
      source_type: 'news-83-chunk-0',
      file_id: '1745737736292',
      page: '82',
      page_content: 'id: 9724 | file_index: index_demo | file_id: 1745737736292 | Economic importance of financial markets in capital allocation...',
      vector_id: 'news-83-chunk-0',
      file_uploaded: 'AIdoctestdata 1.xlsx'
    },
    {
      url: 'AIdoctestdata 1.xlsx',
      summary: 'பங்குச் சந்தை (Stock Market): இது நிறுவனங்களின் பங்குகளை வாங்கவும் விற்கவும் பயன்படுகிறது.',
      source_title: 'AIdoctestdata 1.xlsx',
      source_type: 'news-190-chunk-0',
      file_id: '1745737736292',
      page: '191',
      page_content: 'Stock markets facilitate the buying and selling of company shares, providing liquidity and price discovery mechanisms...',
      vector_id: 'news-190-chunk-0',
      file_uploaded: 'AIdoctestdata 1.xlsx'
    }
  ];

  const sampleWebData = [
    {
      url: 'https://economictimes.indiatimes.com/markets',
      summary: 'Latest market news and financial updates from Economic Times.',
      source_title: 'Economic Times Markets',
      source_type: 'web',
      file_id: '',
      page: '',
      page_content: '',
      vector_id: '',
      file_uploaded: ''
    },
    {
      url: 'https://moneycontrol.com/news/business/markets',
      summary: 'Comprehensive market analysis and business news coverage.',
      source_title: 'MoneyControl Markets',
      source_type: 'web',
      file_id: '',
      page: '',
      page_content: '',
      vector_id: '',
      file_uploaded: ''
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-200 mb-4">
            🎨 Reference UI Comparison
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Comparing the old and new WebpagePreview components for regional languages
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Enhanced UI for Regional Languages */}
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center gap-2">
                <span className="text-2xl">✨</span>
                Enhanced UI (Regional Languages)
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
                New structured design with expandable details, better FAISS data display, and improved visual hierarchy.
              </p>
              
              <div className="space-y-4">
                {sampleFaissData.map((item, index) => (
                  <WebpagePreviewEnhanced
                    key={`enhanced-${index}`}
                    url={item.url}
                    summary={item.summary}
                    source_title={item.source_title}
                    source_type={item.source_type}
                    file_id={item.file_id}
                    page={item.page}
                    page_content={item.page_content}
                    vector_id={item.vector_id}
                    file_uploaded={item.file_uploaded}
                    referenceNumber={index + 1}
                  />
                ))}
              </div>
            </div>
          </div>

          {/* Original UI */}
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center gap-2">
                <span className="text-2xl">📋</span>
                Original UI (English/Web Sources)
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
                Current design optimized for web sources and English content.
              </p>
              
              <div className="space-y-4">
                {sampleWebData.map((item, index) => (
                  <div key={`original-${index}`} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-800/50">
                    <div className="flex items-center gap-2 mb-3">
                      <span className="inline-flex items-center justify-center w-6 h-6 bg-blue-500 text-white text-xs font-bold rounded-full">
                        {index + 1}
                      </span>
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Reference {index + 1}
                      </span>
                    </div>
                    <WebpagePreview
                      url={item.url}
                      summary={item.summary}
                      source_title={item.source_title}
                      source_type={item.source_type}
                      file_id={item.file_id}
                      page={item.page}
                      page_content={item.page_content}
                      vector_id={item.vector_id}
                      file_uploaded={item.file_uploaded}
                      referenceNumber={index + 1}
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Feature Comparison */}
        <div className="mt-12 bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-6 text-center">
            🔍 Feature Comparison
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-green-600 dark:text-green-400">✅ Enhanced UI Features</h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  Expandable/collapsible details
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  Structured FAISS data display
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  Color-coded information sections
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  Copy-to-clipboard functionality
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  Better visual hierarchy
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  Responsive design
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  Regional language optimized
                </li>
              </ul>
            </div>
            
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-blue-600 dark:text-blue-400">📋 Original UI Features</h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  Compact design
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  Web source optimized
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  Favicon display
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  Direct link access
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  Hover interactions
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  English content focused
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Implementation Status */}
        <div className="mt-8 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 p-6 rounded-xl border border-green-200 dark:border-green-800">
          <h2 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center gap-2">
            <span className="text-xl">🎯</span>
            Implementation Status
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg">
              <h3 className="font-medium text-green-600 dark:text-green-400 mb-2">✅ Backend</h3>
              <p className="text-gray-600 dark:text-gray-400">FAISS data extraction working for all 21 sentences with complete metadata</p>
            </div>
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg">
              <h3 className="font-medium text-green-600 dark:text-green-400 mb-2">✅ Frontend Logic</h3>
              <p className="text-gray-600 dark:text-gray-400">Regional language detection and conditional rendering implemented</p>
            </div>
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg">
              <h3 className="font-medium text-green-600 dark:text-green-400 mb-2">✅ Enhanced UI</h3>
              <p className="text-gray-600 dark:text-gray-400">New WebpagePreviewEnhanced component with structured display</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReferenceUIDemo;
