
const { translate } = require('@vitalets/google-translate-api');

// Function to identify capital words that should be preserved
function identifyCapitalWords(text) {
    const capitalWords = [];

    // Find acronyms (2+ consecutive capital letters)
    const acronyms = text.match(/\b[A-Z]{2,}\b/g) || [];
    capitalWords.push(...acronyms);

    // Find mixed-case proper nouns (like iPhone, SpaceX, GitHub)
    const mixedCase = text.match(/\b[A-Z][a-z]*[A-Z][A-Za-z]*\b/g) || [];
    capitalWords.push(...mixedCase);

    // Remove duplicates
    const uniqueWords = [...new Set(capitalWords)];

    if (uniqueWords.length > 0) {
        console.error(`🔍 Identified capital words to preserve: ${uniqueWords.join(', ')}`);
    }

    return uniqueWords;
}

// Function to preserve capital words in translation
function preserveCapitalWords(originalText, translatedText, capitalWords) {
    if (!capitalWords || capitalWords.length === 0) {
        return translatedText;
    }

    let preservedText = translatedText;

    console.error(`🔧 Preserving ${capitalWords.length} capital words in translation`);

    for (const word of capitalWords) {
        // Check if the word is already present in the translation
        if (preservedText.includes(word)) {
            console.error(`✅ Capital word '${word}' already preserved in translation`);
            continue;
        }

        // Strategy: Find the position of the word in the original text and try to preserve it
        const wordRegex = new RegExp(`\\b${word}\\b`, 'gi');
        const originalMatches = originalText.match(wordRegex);

        if (originalMatches) {
            console.error(`🔄 Restoring missing capital word '${word}' in translation`);

            // Simple approach: if the word is missing, try to find a good place to insert it
            // This could be enhanced with more sophisticated word alignment

            // For technical terms and proper nouns, we want to keep them as-is
            // Look for potential translated versions and replace them

            // Common patterns where capital words might get corrupted:
            // 1. Completely missing
            // 2. Translated to local script
            // 3. Broken into parts

            // For now, we'll use a simple strategy of ensuring the word appears
            // in the translation by replacing similar-length words or adding it

            // If the translation is significantly shorter, the word might be missing
            if (translatedText.length < originalText.length * 0.7) {
                // Add the word at the beginning or end contextually
                preservedText = `${word} ${preservedText}`;
                console.error(`🔄 Added missing capital word '${word}' to translation`);
            } else {
                // Try to find and replace potential translations
                // This is a basic implementation - could be much more sophisticated
                const words = preservedText.split(' ');
                let inserted = false;

                // Look for a good position to insert the word
                for (let i = 0; i < words.length; i++) {
                    // If we find a word that might be a translation of our capital word
                    // (similar length, position), replace it
                    if (words[i].length >= word.length - 2 && words[i].length <= word.length + 2) {
                        words[i] = word;
                        preservedText = words.join(' ');
                        inserted = true;
                        console.error(`🔄 Replaced potential translation with '${word}'`);
                        break;
                    }
                }

                if (!inserted) {
                    // As a fallback, add the word at the end
                    preservedText = `${preservedText} ${word}`;
                    console.error(`🔄 Added '${word}' at the end of translation`);
                }
            }
        }
    }

    return preservedText;
}

async function translateText(text, targetLang, sourceLang = 'auto') {
    try {
        // Identify capital words before translation
        const capitalWords = identifyCapitalWords(text);

        const result = await translate(text, {
            from: sourceLang,
            to: targetLang,
            fetchOptions: {
                agent: null // Disable proxy for better reliability
            }
        });

        // Handle different response formats
        let translatedText = result.text || result;
        let detectedLanguage = 'auto';
        let confidence = 0.95;

        // Try to extract language info if available
        if (result.from && result.from.language) {
            detectedLanguage = result.from.language.iso || result.from.language;
            confidence = result.from.language.didYouMean ? 0.8 : 0.95;
        } else if (typeof result === 'string') {
            translatedText = result;
        }

        // Preserve capital words in the translation
        const preservedTranslation = preserveCapitalWords(text, translatedText, capitalWords);

        return {
            success: true,
            translatedText: preservedTranslation,
            detectedLanguage: detectedLanguage,
            confidence: confidence,
            originalText: text,
            capitalWordsPreserved: capitalWords
        };
    } catch (error) {
        return {
            success: false,
            error: error.message,
            originalText: text
        };
    }
}

async function translateFinancialResponse(responseData, targetLang) {
    try {
        const results = {};

        // Translate AI response with capital word preservation and reference number injection
        if (responseData.ai_response) {
            console.error(`🔄 Translating AI response with capital word preservation and reference injection`);

            // First, inject reference numbers into the AI response if sentence analysis is available
            let responseToTranslate = responseData.ai_response;
            if (responseData.sentence_analysis && Array.isArray(responseData.sentence_analysis)) {
                responseToTranslate = injectReferenceNumbers(responseData.ai_response, responseData.sentence_analysis);
                console.error(`🔢 Reference numbers injected into AI response`);
            }

            const aiResult = await translateText(responseToTranslate, targetLang);
            if (aiResult.success) {
                results.ai_response = aiResult.translatedText;
                results.ai_response_capital_words = aiResult.capitalWordsPreserved || [];
            } else {
                // Fallback: return the original text with reference numbers injected
                results.ai_response = responseToTranslate;
                results.ai_response_error = aiResult.error;
                results.ai_response_fallback = true;
                console.error(`⚠️ Translation failed, using fallback with reference numbers: ${aiResult.error}`);
            }
        }

        // Translate related questions with capital word preservation
        if (responseData.related_questions && Array.isArray(responseData.related_questions)) {
            results.related_questions = [];
            results.related_questions_capital_words = [];

            for (const question of responseData.related_questions) {
                if (question && question.trim()) {
                    console.error(`🔄 Translating related question: ${question.substring(0, 50)}...`);
                    const qResult = await translateText(question, targetLang);
                    if (qResult.success) {
                        results.related_questions.push(qResult.translatedText);
                        if (qResult.capitalWordsPreserved) {
                            results.related_questions_capital_words.push(qResult.capitalWordsPreserved);
                        }
                    } else {
                        results.related_questions.push(question); // Keep original if translation fails
                        results.related_questions_capital_words.push([]);
                    }
                } else {
                    results.related_questions.push(question);
                    results.related_questions_capital_words.push([]);
                }
            }
        }

        return {
            success: true,
            data: results
        };
    } catch (error) {
        return {
            success: false,
            error: error.message
        };
    }
}

// Function to inject reference numbers into AI response text
function injectReferenceNumbers(aiResponse, sentenceAnalysis) {
    try {
        let modifiedResponse = aiResponse;

        // Create a map of sentences to reference numbers
        const sentenceToRefMap = new Map();
        sentenceAnalysis.forEach((item, index) => {
            if (item.sentence && item.sentence.trim()) {
                sentenceToRefMap.set(item.sentence.trim(), index + 1);
            }
        });

        // Try to find and inject reference numbers
        sentenceAnalysis.forEach((item, index) => {
            if (item.sentence && item.sentence.trim()) {
                const sentence = item.sentence.trim();
                const refNumber = index + 1;

                // Look for the sentence in the AI response
                const sentenceIndex = modifiedResponse.indexOf(sentence);
                if (sentenceIndex !== -1) {
                    // Insert reference number after the sentence
                    const beforeSentence = modifiedResponse.substring(0, sentenceIndex + sentence.length);
                    const afterSentence = modifiedResponse.substring(sentenceIndex + sentence.length);

                    // Add reference number with space handling
                    const referenceTag = ` [${refNumber}]`;
                    modifiedResponse = beforeSentence + referenceTag + afterSentence;

                    console.error(`🔢 Added reference [${refNumber}] after sentence: "${sentence.substring(0, 50)}..."`);
                }
            }
        });

        // If no direct sentence matches found, add references at the end of paragraphs
        if (modifiedResponse === aiResponse && sentenceAnalysis.length > 0) {
            console.error(`🔢 No direct sentence matches found, adding references at paragraph ends`);

            // Split into paragraphs and add references
            const paragraphs = modifiedResponse.split('\n\n');
            const modifiedParagraphs = paragraphs.map((paragraph, pIndex) => {
                if (paragraph.trim() && pIndex < sentenceAnalysis.length) {
                    const refNumbers = sentenceAnalysis.slice(0, Math.min(3, sentenceAnalysis.length))
                        .map((_, index) => `[${index + 1}]`)
                        .join(' ');
                    return paragraph.trim() + ' ' + refNumbers;
                }
                return paragraph;
            });

            modifiedResponse = modifiedParagraphs.join('\n\n');
        }

        return modifiedResponse;
    } catch (error) {
        console.error(`❌ Error injecting reference numbers: ${error.message}`);
        return aiResponse; // Return original if injection fails
    }
}

// Main execution
async function main() {
    try {
        const args = process.argv.slice(2);
        const command = args[0];
        
        if (command === 'translate') {
            const text = args[1];
            const targetLang = args[2];
            const sourceLang = args[3] || 'auto';
            
            const result = await translateText(text, targetLang, sourceLang);
            console.log(JSON.stringify(result));
            
        } else if (command === 'translate-response') {
            const responseDataStr = args[1];
            const targetLang = args[2];
            
            const responseData = JSON.parse(responseDataStr);
            const result = await translateFinancialResponse(responseData, targetLang);
            console.log(JSON.stringify(result));
            
        } else {
            console.log(JSON.stringify({
                success: false,
                error: 'Invalid command. Use "translate" or "translate-response"'
            }));
        }
    } catch (error) {
        console.log(JSON.stringify({
            success: false,
            error: error.message
        }));
    }
}

main();
