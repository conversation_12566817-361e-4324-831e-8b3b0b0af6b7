#!/usr/bin/env python3
import requests
import json

# Test Tamil query
test_query = 'நிதி சந்தையில் முதலீடு செய்வது எப்படி?'

try:
    response = requests.post(
        'http://localhost:5010/financial_query',
        json={
            'query': test_query,
            'language': 'Tamil',
            'index_name': 'default'
        },
        timeout=30
    )
    
    if response.status_code == 200:
        data = response.json()
        print('✅ Request successful')
        print(f'AI Response: {data.get("ai_response", "No response")[:200]}...')
        print(f'Translation applied: {data.get("translation_applied", False)}')
        print(f'Sentence analysis count: {len(data.get("sentence_analysis", []))}')
        
        # Check for reference numbers in the response
        ai_response = data.get('ai_response', '')
        ref_count = ai_response.count('[') + ai_response.count(']')
        print(f'Reference brackets found: {ref_count}')
        
        if '[1]' in ai_response or '[2]' in ai_response:
            print('✅ Reference numbers found in AI response!')
        else:
            print('❌ No reference numbers found in AI response')
            
        # Show first few sentence analysis items
        sentence_analysis = data.get('sentence_analysis', [])
        if sentence_analysis:
            print(f'\nFirst sentence analysis item:')
            first_item = sentence_analysis[0]
            print(f'  Sentence: {first_item.get("sentence", "")[:100]}...')
            print(f'  Source: {first_item.get("source_title", "N/A")}')
            
    else:
        print(f'❌ Request failed: {response.status_code}')
        print(f'Error: {response.text}')
        
except Exception as e:
    print(f'❌ Error: {str(e)}')
