import React, { useState, useEffect, memo } from 'react';
import Image from 'next/image';
import { PiGlobe, PiArrowSquareOut, PiSpinner, PiLink, PiInfo, PiFile, PiFileText, PiFilePdf, PiMusicNote, PiDatabase, PiHash, PiBookOpen, PiLayers, PiChevronDown, PiChevronUp } from 'react-icons/pi';

interface WebpagePreviewProps {
  url: string;
  summary?: string;
  source_title?: string;
  source_type?: string;
  file_id?: string;
  page?: string;
  page_content?: string;
  vector_id?: string;
  file_uploaded?: string;
  referenceNumber: number;
}

// Using memo to prevent unnecessary re-renders
const WebpagePreview: React.FC<WebpagePreviewProps> = memo(({
  url,
  summary,
  source_title,
  source_type,
  file_id,
  page,
  page_content,
  vector_id,
  file_uploaded,
  referenceNumber
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [faviconUrl, setFaviconUrl] = useState<string | null>(null);
  const [domainName, setDomainName] = useState<string>('');
  const [isExpanded, setIsExpanded] = useState(false);

  // Check if URL is valid (not N/A or Not found)
  const isValidUrl = url && url !== 'N/A' && url !== 'Not found' && url.trim() !== '';

  // Check if this is FAISS data
  const hasFaissData = vector_id || file_uploaded || page_content || file_id;

  // Extract domain name for display
  useEffect(() => {
    if (isValidUrl) {
      try {
        const domain = new URL(url).hostname;
        setDomainName(domain.replace('www.', ''));
      } catch (error) {
        setDomainName(url);
      }
    } else {
      setDomainName('');
    }
  }, [url, isValidUrl]);

  // Get favicon URL
  useEffect(() => {
    if (isValidUrl) {
      try {
        const domain = new URL(url).origin;
        setFaviconUrl(`${domain}/favicon.ico`);
        // Set loading to false after a short delay to avoid flickering
        const timer = setTimeout(() => setIsLoading(false), 300);
        return () => clearTimeout(timer);
      } catch (error) {
        setFaviconUrl(null);
        setIsLoading(false);
      }
    } else {
      setFaviconUrl(null);
      setIsLoading(false);
    }
  }, [url, isValidUrl]);

  // Function to get appropriate icon based on source type
  const getSourceIcon = () => {
    if (!source_type) return <PiFile className="text-primaryColor" />;
    
    const type = source_type.toLowerCase();
    if (type.includes('pdf')) return <PiFilePdf className="text-red-500" />;
    if (type.includes('audio') || type.includes('mp3') || type.includes('wav')) return <PiMusicNote className="text-purple-500" />;
    if (type.includes('text') || type.includes('doc')) return <PiFileText className="text-blue-500" />;
    return <PiFile className="text-primaryColor" />;
  };

  // Function to format source type for display
  const formatSourceType = (type?: string) => {
    if (!type) return 'Document';
    
    // Clean up the source type for better display
    return type.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  // Function to clean and format summary text for better display
  const formatSummary = (summaryText?: string) => {
    if (!summaryText) return '';
    
    // Clean up the summary text by removing excessive formatting and truncating
    let cleaned = summaryText
      .replace(/^Source:\s*/i, '') // Remove "Source:" prefix
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .trim();
    
    // If it's too long, truncate it intelligently
    if (cleaned.length > 200) {
      // Try to find a good breaking point (sentence end)
      const sentences = cleaned.split(/[.!?]+/);
      if (sentences.length > 1 && sentences[0].length < 150) {
        cleaned = sentences[0] + '.';
      } else {
        // Fallback to character limit with word boundary
        cleaned = cleaned.substring(0, 180);
        const lastSpace = cleaned.lastIndexOf(' ');
        if (lastSpace > 100) {
          cleaned = cleaned.substring(0, lastSpace) + '...';
        } else {
          cleaned = cleaned + '...';
        }
      }
    }
    
    return cleaned;
  };

  // Function to handle explicit visit button click
  const handleVisitClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    if (isValidUrl) {
      window.open(url, '_blank', 'noopener,noreferrer');
    }
  };

  // Debug logging to see what we're receiving
  console.log('WebpagePreview props:', {
    url,
    summary,
    source_title,
    source_type,
    file_id,
    page,
    page_content,
    vector_id,
    file_uploaded,
    referenceNumber,
    isValidUrl
  });

  return (
    <div
      className="p-4 bg-white dark:bg-gray-800 shadow-xl rounded-xl border border-primaryColor/20 z-50 w-96 max-w-sm"
      // Using onMouseDown instead of onClick for better performance
      onMouseDown={(e) => e.stopPropagation()}
    >
      {/* Header with reference number and source type */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-r from-primaryColor/20 to-blue-500/20 rounded-full">
            {isValidUrl && faviconUrl ? (
              <Image
                src={faviconUrl}
                alt={domainName}
                width={16}
                height={16}
                onError={() => setFaviconUrl(null)}
                className="w-4 h-4"
                unoptimized // Skip image optimization for better performance
              />
            ) : (
              getSourceIcon()
            )}
          </div>
          <div className="flex flex-col">
            <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
              {isValidUrl ? formatSourceType(source_type) : (source_title ? 'Knowledge Base' : formatSourceType(source_type))}
            </span>
            {!isValidUrl && source_title && (
              <span className="text-xs text-gray-400 dark:text-gray-500">
                Local Reference
              </span>
            )}
          </div>
        </div>
        <span className="text-xs font-bold text-white bg-gradient-to-r from-primaryColor to-blue-600 px-2.5 py-1 rounded-full shadow-sm">
          [{referenceNumber}]
        </span>
      </div>

      {/* Source Title - Most prominent with better styling */}
      {source_title && (
        <div className="mb-3 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700/50 dark:to-gray-600/50 rounded-lg border-l-4 border-primaryColor/40">
          <div className="flex items-start gap-2">
            <PiFileText className="text-primaryColor mt-0.5 flex-shrink-0" />
            <div className="flex-1 min-w-0">
              <p className="text-xs font-medium text-primaryColor dark:text-primaryColor/80 mb-1">
                {isValidUrl ? 'Document Title' : 'Source Document'}
              </p>
              <h4 
                className="text-sm font-bold text-gray-900 dark:text-gray-100 leading-tight break-words"
                style={{
                  wordBreak: 'break-word',
                  overflowWrap: 'break-word',
                  whiteSpace: 'normal',
                  display: 'block'
                }}
              >
                {source_title}
              </h4>
            </div>
          </div>
        </div>
      )}

      {/* File ID and Page information - for all documents */}
      {(file_id || page) && (
        <div className="mb-3 p-3 bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-indigo-900/20 dark:to-blue-900/20 rounded-lg border border-indigo-200 dark:border-indigo-800">
          <div className="flex items-start gap-2 mb-2">
            <PiInfo className="text-indigo-600 dark:text-indigo-400 mt-0.5 flex-shrink-0" />
            <div className="flex-1 min-w-0">
              <p className="text-xs font-medium text-indigo-700 dark:text-indigo-300 mb-2">
                Document Details
              </p>
              <div className="flex flex-wrap items-center gap-3 text-xs">
                {file_id && String(file_id) !== "Unknown" && String(file_id) !== "N/A" && String(file_id).trim() !== "" && (
                  <div className="flex items-center gap-1 bg-white dark:bg-gray-800 px-2 py-1 rounded border">
                    <PiFile className="text-indigo-600 dark:text-indigo-400" />
                    <span className="text-indigo-700 dark:text-indigo-300 font-medium">
                      ID: {file_id}
                    </span>
                  </div>
                )}
                {page && String(page) !== "Unknown" && String(page) !== "N/A" && String(page).trim() !== "" && (
                  <div className="flex items-center gap-1 bg-white dark:bg-gray-800 px-2 py-1 rounded border">
                    <PiFileText className="text-indigo-600 dark:text-indigo-400" />
                    <span className="text-indigo-700 dark:text-indigo-300 font-medium">
                      Page: {page}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* FAISS Data for Regional Languages - Vector ID and File Uploaded */}
      {(vector_id || file_uploaded) && (
        <div className="mb-3 p-3 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
          <div className="flex items-start gap-2 mb-2">
            <PiInfo className="text-purple-600 dark:text-purple-400 mt-0.5 flex-shrink-0" />
            <div className="flex-1 min-w-0">
              <p className="text-xs font-medium text-purple-700 dark:text-purple-300 mb-2">
                FAISS Vector Data
              </p>
              <div className="flex flex-wrap items-center gap-3 text-xs">
                {vector_id && String(vector_id) !== "unknown" && String(vector_id) !== "N/A" && String(vector_id).trim() !== "" && (
                  <div className="flex items-center gap-1 bg-white dark:bg-gray-800 px-2 py-1 rounded border">
                    <PiFileText className="text-purple-600 dark:text-purple-400" />
                    <span className="text-purple-700 dark:text-purple-300 font-medium">
                      Vector: {vector_id}
                    </span>
                  </div>
                )}
                {file_uploaded && String(file_uploaded) !== "Unknown" && String(file_uploaded) !== "N/A" && String(file_uploaded).trim() !== "" && (
                  <div className="flex items-center gap-1 bg-white dark:bg-gray-800 px-2 py-1 rounded border">
                    <PiFile className="text-purple-600 dark:text-purple-400" />
                    <span className="text-purple-700 dark:text-purple-300 font-medium">
                      File: {file_uploaded}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Page Content for Regional Languages */}
      {page_content && page_content.trim() !== "" && (
        <div className="mb-3 p-3 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg border border-green-200 dark:border-green-800">
          <div className="flex items-start gap-2">
            <PiFileText className="text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
            <div className="flex-1 min-w-0">
              <p className="text-xs font-medium text-green-700 dark:text-green-300 mb-2">
                Page Content
              </p>
              <p
                className="text-xs text-green-700 dark:text-green-200 leading-relaxed"
                style={{
                  wordBreak: 'break-word',
                  overflowWrap: 'break-word',
                  whiteSpace: 'normal'
                }}
              >
                {page_content.length > 200 ? `${page_content.substring(0, 200)}...` : page_content}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* URL display - only if valid */}
      {isValidUrl && (
        <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mb-3 bg-gray-50 dark:bg-gray-700/50 p-2 rounded-lg">
          <PiLink className="mr-2 text-primaryColor flex-shrink-0" />
          <span className="truncate font-mono">
            {domainName || url}
          </span>
        </div>
      )}

      {/* Summary section - Professional styling with cleaned text */}
      {summary && formatSummary(summary) && (
        <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700/50 dark:to-gray-600/50 p-3 rounded-lg mb-3 border-l-4 border-primaryColor/30">
          <div className="flex items-start gap-2">
            <PiInfo className="text-primaryColor mt-0.5 flex-shrink-0" />
            <div className="flex-1 min-w-0">
              <p className="text-xs font-medium text-gray-600 dark:text-gray-300 mb-1">Content Preview</p>
              <p 
                className="text-xs text-gray-700 dark:text-gray-200 leading-relaxed"
                style={{
                  wordBreak: 'break-word',
                  overflowWrap: 'break-word',
                  whiteSpace: 'normal'
                }}
              >
                {formatSummary(summary)}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Preview section - Contextual based on source type */}
      <div className="relative w-full h-20 mb-3 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-600">
        {isLoading && isValidUrl && (
          <div className="absolute inset-0 flex items-center justify-center z-10">
            <div className="animate-pulse flex flex-col items-center">
              <div className="w-6 h-6 bg-primaryColor/20 rounded-full mb-1 flex items-center justify-center">
                <PiSpinner className="text-primaryColor animate-spin text-sm" />
              </div>
              <div className="h-1 w-16 bg-primaryColor/20 rounded"></div>
            </div>
          </div>
        )}

        <div className="absolute inset-0 flex items-center justify-center p-2">
          <div className="text-center">
            <div className="mb-1">{getSourceIcon()}</div>
            <p className="text-xs text-gray-500 dark:text-gray-400 font-medium break-words leading-tight">
              {isValidUrl ? `Web: ${domainName}` : (source_title ? 'Knowledge Base' : 'Document Reference')}
            </p>
          </div>
        </div>
      </div>

      {/* Action button - only show if URL is valid */}
      {isValidUrl && (
        <div className="flex items-center justify-end">
          <button
            className="flex items-center gap-2 text-white bg-gradient-to-r from-primaryColor to-blue-600 px-3 py-1.5 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 text-xs font-medium"
            onClick={handleVisitClick}
          >
            <span>Visit Source</span>
            <PiArrowSquareOut className="text-sm" />
          </button>
        </div>
      )}

      {/* Enhanced display when no valid URL but has source title */}
      {/* {!isValidUrl && source_title && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
          <div className="flex items-center gap-2 mb-2">
            <PiFile className="text-blue-600 dark:text-blue-400" />
            <span className="text-xs font-medium text-blue-700 dark:text-blue-300">
              Document Reference
            </span>
          </div>
          <p className="text-xs text-blue-600 dark:text-blue-400 leading-relaxed">
            This information is sourced from a local document in the knowledge base.
          </p>
        </div>
      )} */}

      {/* Fallback when no source title and no valid URL */}
      {!isValidUrl && !source_title && (
        <div className="bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 rounded-lg p-3 text-center">
          <div className="flex items-center justify-center gap-2 mb-1">
            <PiInfo className="text-gray-500 dark:text-gray-400" />
            <span className="text-xs font-medium text-gray-600 dark:text-gray-300">
              Reference Information
            </span>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Source information is not available for this reference.
          </p>
        </div>
      )}
    </div>
  );
});

// Add display name for debugging
WebpagePreview.displayName = 'WebpagePreview';

export default WebpagePreview;
